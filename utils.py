import streamlit as st
import logging
from functools import wraps
from typing import Tuple, Any
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def handle_azure_exceptions(func):
    """Decorator to handle Azure service exceptions"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Azure operation failed in {func.__name__}: {str(e)}")
            return False, f"Azure service error: {str(e)}"
    return wrapper

def validate_filename(filename: str) -> Tuple[bool, str]:
    """Validate filename for Azure blob storage"""
    if not filename:
        return False, "Filename cannot be empty"
    
    # Check for invalid characters
    invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
    for char in invalid_chars:
        if char in filename:
            return False, f"Filename contains invalid character: {char}"
    
    # Check length
    if len(filename) > 255:
        return False, "Filename too long (max 255 characters)"
    
    return True, "Filename is valid"

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

def show_success_message(filename: str, container: str):
    """Show success message with file details"""
    st.success("🎉 Upload Successful!")
    st.balloons()
    
    col1, col2 = st.columns(2)
    with col1:
        st.info(f"**File:** {filename}")
    with col2:
        st.info(f"**Container:** {container}")

def show_error_message(error: str):
    """Show formatted error message"""
    st.error(f"❌ Upload Failed: {error}")
    logger.error(f"Upload error: {error}")

def create_queue_message(filename: str, original_filename: str, file_size: int, container: str) -> dict:
    """Create standardized queue message"""
    return {
        "filename": filename,
        "original_filename": original_filename,
        "upload_timestamp": datetime.now().isoformat(),
        "file_size_bytes": file_size,
        "file_size_formatted": format_file_size(file_size),
        "container": container,
        "status": "uploaded"
    }
