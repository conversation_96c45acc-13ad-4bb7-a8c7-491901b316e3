import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for Azure services"""
    
    # Azure Storage Configuration
    AZURE_STORAGE_CONNECTION_STRING = os.getenv('AZURE_STORAGE_CONNECTION_STRING')
    AZURE_BLOB_CONTAINER_NAME = os.getenv('AZURE_BLOB_CONTAINER_NAME')
    AZURE_QUEUE_NAME = os.getenv('AZURE_QUEUE_NAME')
    
    # File upload settings
    MAX_FILE_SIZE_MB = 100
    ALLOWED_EXTENSIONS = ['.xlsx', '.csv']
    
    @classmethod
    def validate_config(cls):
        """Validate that all required configuration is present"""
        required_configs = [
            'AZURE_STORAGE_CONNECTION_STRING',
            'AZURE_BLOB_CONTAINER_NAME',
            'AZURE_QUEUE_NAME'
        ]
        
        missing_configs = []
        for config in required_configs:
            if not getattr(cls, config):
                missing_configs.append(config)
        
        if missing_configs:
            raise ValueError(f"Missing required configuration: {', '.join(missing_configs)}")
        
        return True
