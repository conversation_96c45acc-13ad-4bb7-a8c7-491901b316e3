# Azure Blob Upload Streamlit UI

A Streamlit web application for uploading CSV and XLSX files to Azure Blob Storage with automatic queue notifications.

## Features

- 📁 Upload CSV and XLSX files to Azure Blob Storage
- 🔄 Automatic file validation (size, type)
- 📝 Custom filename support
- 📊 File preview before upload
- 📨 Queue notifications with file metadata
- 🎨 Clean, intuitive user interface
- ⚡ Real-time upload progress

## Prerequisites

- Python 3.8 or higher
- Azure Storage Account
- Azure Blob Container
- Azure Storage Queue

## Installation

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd blob-upload-streamlit-ui
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Azure credentials**
   - Copy `.env.example` to `.env`
   - Fill in your Azure Storage credentials:
   ```env
   AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=your_account_name;AccountKey=your_account_key;EndpointSuffix=core.windows.net
   AZURE_BLOB_CONTAINER_NAME=your_container_name
   AZURE_QUEUE_NAME=your_queue_name
   ```

## Azure Setup

### 1. Create Storage Account
1. Go to Azure Portal
2. Create a new Storage Account
3. Note down the connection string from "Access keys"

### 2. Create Blob Container
1. In your Storage Account, go to "Containers"
2. Create a new container
3. Set access level as needed

### 3. Create Storage Queue
1. In your Storage Account, go to "Queues"
2. Create a new queue
3. Note down the queue name

## Usage

1. **Start the application**
   ```bash
   streamlit run app.py
   ```

2. **Access the web interface**
   - Open your browser to `http://localhost:8501`

3. **Upload files**
   - Select a CSV or XLSX file
   - Optionally provide a custom filename
   - Preview the file data
   - Click "Upload to Azure"

## File Specifications

- **Supported formats**: CSV, XLSX
- **Maximum file size**: 100 MB (configurable)
- **File validation**: Automatic type and size checking

## Queue Message Format

When a file is uploaded, a JSON message is sent to the Azure Queue:

```json
{
  "filename": "uploaded_file.xlsx",
  "original_filename": "original_file.xlsx",
  "upload_timestamp": "2024-01-01T12:00:00.000000",
  "file_size_bytes": 1024,
  "file_size_formatted": "1.0 KB",
  "container": "your_container_name",
  "status": "uploaded"
}
```

## Configuration

Edit `config.py` to modify:
- Maximum file size
- Allowed file extensions
- Other application settings

## Project Structure

```
blob-upload-streamlit-ui/
├── app.py              # Main Streamlit application
├── config.py           # Configuration management
├── utils.py            # Utility functions and error handling
├── requirements.txt    # Python dependencies
├── .env.example       # Environment variables template
└── README.md          # This file
```

## Troubleshooting

### Common Issues

1. **"Azure services not properly configured"**
   - Check your `.env` file exists and has correct values
   - Verify Azure connection string is valid

2. **"Failed to upload file"**
   - Check container exists and is accessible
   - Verify storage account permissions

3. **"Failed to send message to queue"**
   - Check queue exists
   - Verify queue permissions

### Logs

The application logs errors to the console. Check the terminal running Streamlit for detailed error messages.

## Development

To extend the application:

1. **Add new file types**: Update `ALLOWED_EXTENSIONS` in `config.py`
2. **Modify queue message**: Edit `create_queue_message()` in `utils.py`
3. **Add new features**: Extend the main application in `app.py`

## Security Notes

- Never commit your `.env` file to version control
- Use Azure Key Vault for production deployments
- Consider implementing authentication for production use
- Regularly rotate your Azure storage keys

## License

This project is open source. Feel free to modify and distribute as needed.
