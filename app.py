import streamlit as st
import pandas as pd
import os
from datetime import datetime
from azure.storage.blob import BlobServiceClient
from azure.storage.queue import QueueServiceClient
from config import Config
from utils import (
    handle_azure_exceptions,
    validate_filename,
    format_file_size,
    show_success_message,
    show_error_message,
    create_queue_message
)
import json

# Page configuration
st.set_page_config(
    page_title="Azure Blob File Upload",
    page_icon="📁",
    layout="wide"
)

class AzureStorageManager:
    """Handles Azure Blob Storage and Queue operations"""
    
    def __init__(self):
        try:
            Config.validate_config()
            self.blob_service_client = BlobServiceClient.from_connection_string(
                Config.AZURE_STORAGE_CONNECTION_STRING
            )
            self.queue_service_client = QueueServiceClient.from_connection_string(
                Config.AZURE_STORAGE_CONNECTION_STRING
            )
            self.container_name = Config.AZURE_BLOB_CONTAINER_NAME
            self.queue_name = Config.AZURE_QUEUE_NAME
        except Exception as e:
            st.error(f"Failed to initialize Azure services: {str(e)}")
            self.blob_service_client = None
            self.queue_service_client = None
    
    def upload_file_to_blob(self, file_data, blob_name):
        """Upload file to Azure Blob Storage"""
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=blob_name
            )
            blob_client.upload_blob(file_data, overwrite=True)
            return True, f"File uploaded successfully to blob: {blob_name}"
        except Exception as e:
            return False, f"Failed to upload file: {str(e)}"
    
    def send_message_to_queue(self, message):
        """Send message to Azure Queue"""
        try:
            queue_client = self.queue_service_client.get_queue_client(self.queue_name)
            queue_client.send_message(json.dumps(message))
            return True, "Message sent to queue successfully"
        except Exception as e:
            return False, f"Failed to send message to queue: {str(e)}"

def validate_file(uploaded_file):
    """Validate uploaded file"""
    if uploaded_file is None:
        return False, "No file uploaded"
    
    # Check file extension
    file_extension = os.path.splitext(uploaded_file.name)[1].lower()
    if file_extension not in Config.ALLOWED_EXTENSIONS:
        return False, f"File type not allowed. Supported types: {', '.join(Config.ALLOWED_EXTENSIONS)}"
    
    # Check file size
    file_size_mb = uploaded_file.size / (1024 * 1024)
    if file_size_mb > Config.MAX_FILE_SIZE_MB:
        return False, f"File size ({file_size_mb:.2f} MB) exceeds maximum allowed size ({Config.MAX_FILE_SIZE_MB} MB)"
    
    return True, "File is valid"

def preview_file_data(uploaded_file):
    """Preview file data"""
    try:
        file_extension = os.path.splitext(uploaded_file.name)[1].lower()
        
        if file_extension == '.csv':
            df = pd.read_csv(uploaded_file)
        elif file_extension == '.xlsx':
            df = pd.read_excel(uploaded_file)
        else:
            return None
        
        return df
    except Exception as e:
        st.error(f"Error reading file: {str(e)}")
        return None

def main():
    st.title("📁 Azure Blob File Upload")
    st.markdown("Upload CSV or XLSX files to Azure Blob Storage and send notifications to Azure Queue")
    
    # Initialize Azure Storage Manager
    storage_manager = AzureStorageManager()
    
    if storage_manager.blob_service_client is None:
        st.error("❌ Azure services not properly configured. Please check your configuration.")
        st.info("Make sure to create a `.env` file based on `.env.example` with your Azure credentials.")
        return
    
    # Sidebar for configuration info
    with st.sidebar:
        st.header("Configuration")
        st.info(f"**Container:** {Config.AZURE_BLOB_CONTAINER_NAME}")
        st.info(f"**Queue:** {Config.AZURE_QUEUE_NAME}")
        st.info(f"**Max File Size:** {Config.MAX_FILE_SIZE_MB} MB")
        st.info(f"**Allowed Types:** {', '.join(Config.ALLOWED_EXTENSIONS)}")
    
    # Main upload interface
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("File Upload")
        
        # File uploader
        uploaded_file = st.file_uploader(
            "Choose a file",
            type=['csv', 'xlsx'],
            help=f"Upload CSV or XLSX files (max {Config.MAX_FILE_SIZE_MB} MB)"
        )
        
        # Custom file name input
        custom_filename = st.text_input(
            "Custom filename (optional)",
            help="Leave empty to use original filename"
        )
        
        if uploaded_file is not None:
            # Validate file
            is_valid, validation_message = validate_file(uploaded_file)
            
            if is_valid:
                st.success(f"✅ {validation_message}")
                
                # Determine final filename
                if custom_filename:
                    file_extension = os.path.splitext(uploaded_file.name)[1]
                    final_filename = f"{custom_filename}{file_extension}"
                else:
                    final_filename = uploaded_file.name
                
                st.info(f"**Final filename:** {final_filename}")
                
                # Preview file data
                with st.expander("📊 Preview File Data", expanded=False):
                    df = preview_file_data(uploaded_file)
                    if df is not None:
                        st.write(f"**Shape:** {df.shape[0]} rows × {df.shape[1]} columns")
                        st.dataframe(df.head(10))
                
                # Upload button
                if st.button("🚀 Upload to Azure", type="primary"):
                    with st.spinner("Uploading file..."):
                        # Reset file pointer
                        uploaded_file.seek(0)
                        
                        # Upload to blob storage
                        success, message = storage_manager.upload_file_to_blob(
                            uploaded_file.getvalue(),
                            final_filename
                        )
                        
                        if success:
                            st.success(message)
                            
                            # Send message to queue
                            queue_message = {
                                "filename": final_filename,
                                "original_filename": uploaded_file.name,
                                "upload_timestamp": datetime.now().isoformat(),
                                "file_size_bytes": uploaded_file.size,
                                "container": Config.AZURE_BLOB_CONTAINER_NAME
                            }
                            
                            queue_success, queue_message_result = storage_manager.send_message_to_queue(queue_message)
                            
                            if queue_success:
                                st.success("✅ File name sent to queue successfully!")
                                st.json(queue_message)
                            else:
                                st.warning(f"⚠️ File uploaded but queue notification failed: {queue_message_result}")
                        else:
                            st.error(f"❌ {message}")
            else:
                st.error(f"❌ {validation_message}")
    
    with col2:
        st.header("Upload History")
        st.info("Recent uploads will appear here")
        # This could be extended to show recent uploads from blob storage

if __name__ == "__main__":
    main()
